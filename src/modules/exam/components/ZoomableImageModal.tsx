import React, {useRef, useState} from 'react';
import {
  Modal,
  View,
  Dimensions,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  Animated,
  PanResponder,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

interface ZoomableImageModalProps {
  visible: boolean;
  imageUri: string;
  onClose: () => void;
}

const ZoomableImageModal: React.FC<ZoomableImageModalProps> = ({
  visible,
  imageUri,
  onClose,
}) => {
  const [scale, setScale] = useState(1);
  const [lastScale, setLastScale] = useState(1);
  const [offsetX, setOffsetX] = useState(0);
  const [offsetY, setOffsetY] = useState(0);
  const [lastOffsetX, setLastOffsetX] = useState(0);
  const [lastOffsetY, setLastOffsetY] = useState(0);
  const [lastTap, setLastTap] = useState(0);

  const scaleValue = useRef(new Animated.Value(1)).current;
  const translateX = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(0)).current;
  const initialDistance = useRef<number | null>(null);

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderGrant: () => {
        setLastOffsetX(offsetX);
        setLastOffsetY(offsetY);
      },
      onPanResponderMove: (evt, gestureState) => {
        const touches = evt.nativeEvent.touches;
        const numberOfTouches = touches ? touches.length : 1;

        if (numberOfTouches === 1 && scale > 1) {
          // Single finger - pan (only when zoomed in)
          const newOffsetX = lastOffsetX + gestureState.dx;
          const newOffsetY = lastOffsetY + gestureState.dy;

          // Limit panning to image bounds
          const maxOffsetX = (screenWidth * (scale - 1)) / 2;
          const maxOffsetY = (screenHeight * 0.8 * (scale - 1)) / 2;

          const boundedOffsetX = Math.max(
            -maxOffsetX,
            Math.min(maxOffsetX, newOffsetX),
          );
          const boundedOffsetY = Math.max(
            -maxOffsetY,
            Math.min(maxOffsetY, newOffsetY),
          );

          setOffsetX(boundedOffsetX);
          setOffsetY(boundedOffsetY);

          translateX.setValue(boundedOffsetX);
          translateY.setValue(boundedOffsetY);
        } else if (numberOfTouches === 2) {
          // Two fingers - pinch to zoom
          const touch1 = touches[0];
          const touch2 = touches[1];

          const distance = Math.sqrt(
            Math.pow(touch2.pageX - touch1.pageX, 2) +
              Math.pow(touch2.pageY - touch1.pageY, 2),
          );

          if (!initialDistance.current) {
            initialDistance.current = distance;
          } else {
            const newScale = Math.max(
              0.5,
              Math.min(4, lastScale * (distance / initialDistance.current)),
            );
            setScale(newScale);
            scaleValue.setValue(newScale);
          }
        }
      },
      onPanResponderRelease: () => {
        setLastScale(scale);
        setLastOffsetX(offsetX);
        setLastOffsetY(offsetY);
        initialDistance.current = null;

        // Reset if zoomed out too much
        if (scale < 1) {
          resetZoom();
        }
      },
    }),
  ).current;

  const resetZoom = () => {
    setScale(1);
    setLastScale(1);
    setOffsetX(0);
    setOffsetY(0);
    setLastOffsetX(0);
    setLastOffsetY(0);

    Animated.parallel([
      Animated.timing(scaleValue, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(translateX, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handleDoubleTap = () => {
    const now = Date.now();
    const DOUBLE_TAP_DELAY = 300;

    if (now - lastTap < DOUBLE_TAP_DELAY) {
      // Double tap detected
      if (scale > 1) {
        resetZoom();
      } else {
        // Zoom to 2x
        setScale(2);
        setLastScale(2);
        Animated.timing(scaleValue, {
          toValue: 2,
          duration: 300,
          useNativeDriver: true,
        }).start();
      }
    }
    setLastTap(now);
  };

  const handleClose = () => {
    resetZoom();
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={handleClose}
      statusBarTranslucent={true}>
      <StatusBar backgroundColor="rgba(0,0,0,0.9)" barStyle="light-content" />
      <View style={styles.modalContainer}>
        {/* Background overlay - tap to close */}
        <TouchableOpacity
          style={styles.overlay}
          activeOpacity={1}
          onPress={handleClose}
        />

        {/* Close button */}
        <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
          <Winicon
            src="outline/layout/xmark"
            size={24}
            color={ColorThemes.light.Neutral_Background_Color_Absolute}
          />
        </TouchableOpacity>

        {/* Zoomable image */}
        <View style={styles.imageContainer} {...panResponder.panHandlers}>
          <Animated.View
            style={[
              styles.animatedImageContainer,
              {
                transform: [
                  {scale: scaleValue},
                  {translateX: translateX},
                  {translateY: translateY},
                ],
              },
            ]}>
            <TouchableOpacity
              activeOpacity={1}
              onPress={handleDoubleTap}
              style={styles.imageWrapper}>
              <FastImage
                source={{uri: imageUri}}
                style={styles.image}
                resizeMode={FastImage.resizeMode.contain}
              />
            </TouchableOpacity>
          </Animated.View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  closeButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 1000,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    padding: 8,
  },
  imageContainer: {
    width: screenWidth,
    height: screenHeight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  animatedImageContainer: {
    width: screenWidth,
    height: screenHeight * 0.8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageWrapper: {
    width: '100%',
    height: '100%',
  },
  image: {
    width: '100%',
    height: '100%',
  },
});

export default ZoomableImageModal;
