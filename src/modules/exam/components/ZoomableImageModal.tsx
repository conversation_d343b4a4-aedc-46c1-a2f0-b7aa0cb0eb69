import React, {useRef, useState} from 'react';
import {
  Modal,
  View,
  Dimensions,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  Animated,
  PanResponder,
  Text,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import {Winicon} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {ScrollView} from 'react-native-gesture-handler';

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

interface ZoomableImageModalProps {
  visible: boolean;
  imageUri: string;
  onClose: () => void;
}

const ZoomableImageModal: React.FC<ZoomableImageModalProps> = ({
  visible,
  imageUri,
  onClose,
}) => {
  const [scale, setScale] = useState(1);
  const [lastScale, setLastScale] = useState(1);
  const [offsetX, setOffsetX] = useState(0);
  const [offsetY, setOffsetY] = useState(0);
  const [lastOffsetX, setLastOffsetX] = useState(0);
  const [lastOffsetY, setLastOffsetY] = useState(0);
  const [lastTap, setLastTap] = useState(0);

  const scaleValue = useRef(new Animated.Value(1)).current;
  const translateX = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(0)).current;
  const initialDistance = useRef<number | null>(null);

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        // Allow movement if zoomed in or if it's a pinch gesture
        const touches = evt.nativeEvent.touches;
        const numberOfTouches = touches ? touches.length : 1;
        return (
          numberOfTouches === 2 ||
          scale > 1 ||
          Math.abs(gestureState.dx) > 2 ||
          Math.abs(gestureState.dy) > 2
        );
      },
      onPanResponderGrant: () => {
        setLastOffsetX(offsetX);
        setLastOffsetY(offsetY);
      },
      onPanResponderMove: (evt, gestureState) => {
        const touches = evt.nativeEvent.touches;
        const numberOfTouches = touches ? touches.length : 1;

        if (numberOfTouches === 1) {
          if (scale > 1) {
            // Single finger - pan when zoomed in
            const newOffsetX = lastOffsetX + gestureState.dx;
            const newOffsetY = lastOffsetY + gestureState.dy;

            // Calculate maximum pan distance based on zoom level
            // The more zoomed in, the more we can pan
            const imageWidth = screenWidth * scale;
            const imageHeight = screenHeight * 0.8 * scale;

            const maxOffsetX = Math.max(0, (imageWidth - screenWidth) / 2);
            const maxOffsetY = Math.max(
              0,
              (imageHeight - screenHeight * 0.8) / 2,
            );

            // Apply bounds to prevent panning too far
            const boundedOffsetX = Math.max(
              -maxOffsetX,
              Math.min(maxOffsetX, newOffsetX),
            );
            const boundedOffsetY = Math.max(
              -maxOffsetY,
              Math.min(maxOffsetY, newOffsetY),
            );

            setOffsetX(boundedOffsetX);
            setOffsetY(boundedOffsetY);

            translateX.setValue(boundedOffsetX);
            translateY.setValue(boundedOffsetY);
          }
        } else if (numberOfTouches === 2) {
          // Two fingers - pinch to zoom
          const touch1 = touches[0];
          const touch2 = touches[1];

          const distance = Math.sqrt(
            Math.pow(touch2.pageX - touch1.pageX, 2) +
              Math.pow(touch2.pageY - touch1.pageY, 2),
          );

          if (!initialDistance.current) {
            initialDistance.current = distance;
          } else {
            const newScale = Math.max(
              0.5,
              Math.min(4, lastScale * (distance / initialDistance.current)),
            );
            setScale(newScale);
            scaleValue.setValue(newScale);
          }
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        setLastScale(scale);
        setLastOffsetX(offsetX);
        setLastOffsetY(offsetY);
        initialDistance.current = null;

        // Reset if zoomed out too much
        if (scale < 1) {
          resetZoom();
          return;
        }

        // Add momentum scrolling for smooth panning
        if (
          scale > 1 &&
          (Math.abs(gestureState.vx) > 0.5 || Math.abs(gestureState.vy) > 0.5)
        ) {
          const momentumX = gestureState.vx * 100; // Adjust multiplier for desired momentum
          const momentumY = gestureState.vy * 100;

          const finalOffsetX = offsetX + momentumX;
          const finalOffsetY = offsetY + momentumY;

          // Calculate bounds for momentum
          const imageWidth = screenWidth * scale;
          const imageHeight = screenHeight * 0.8 * scale;
          const maxOffsetX = Math.max(0, (imageWidth - screenWidth) / 2);
          const maxOffsetY = Math.max(
            0,
            (imageHeight - screenHeight * 0.8) / 2,
          );

          const boundedFinalX = Math.max(
            -maxOffsetX,
            Math.min(maxOffsetX, finalOffsetX),
          );
          const boundedFinalY = Math.max(
            -maxOffsetY,
            Math.min(maxOffsetY, finalOffsetY),
          );

          // Animate to final position with momentum
          Animated.parallel([
            Animated.timing(translateX, {
              toValue: boundedFinalX,
              duration: 400,
              useNativeDriver: true,
            }),
            Animated.timing(translateY, {
              toValue: boundedFinalY,
              duration: 400,
              useNativeDriver: true,
            }),
          ]).start(() => {
            setOffsetX(boundedFinalX);
            setOffsetY(boundedFinalY);
            setLastOffsetX(boundedFinalX);
            setLastOffsetY(boundedFinalY);
          });
        }
      },
    }),
  ).current;

  const resetZoom = () => {
    setScale(1);
    setLastScale(1);
    setOffsetX(0);
    setOffsetY(0);
    setLastOffsetX(0);
    setLastOffsetY(0);

    Animated.parallel([
      Animated.timing(scaleValue, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(translateX, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handleDoubleTap = () => {
    const now = Date.now();
    const DOUBLE_TAP_DELAY = 300;

    if (now - lastTap < DOUBLE_TAP_DELAY) {
      // Double tap detected
      if (scale > 1) {
        resetZoom();
      } else {
        // Zoom to 2x and center the image
        const newScale = 2;
        setScale(newScale);
        setLastScale(newScale);

        // Reset position to center when zooming in
        setOffsetX(0);
        setOffsetY(0);
        setLastOffsetX(0);
        setLastOffsetY(0);

        Animated.parallel([
          Animated.timing(scaleValue, {
            toValue: newScale,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(translateX, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(translateY, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start();
      }
    }
    setLastTap(now);
  };

  const handleClose = () => {
    resetZoom();
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={handleClose}
      statusBarTranslucent={true}>
      <StatusBar backgroundColor="rgba(0,0,0,0.9)" barStyle="light-content" />
      <View style={styles.modalContainer}>
        {/* Background overlay - tap to close */}
        <TouchableOpacity
          style={styles.overlay}
          activeOpacity={1}
          onPress={handleClose}
        />

        {/* Close button */}
        <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
          <Winicon
            src="outline/layout/xmark"
            size={24}
            color={ColorThemes.light.Neutral_Background_Color_Absolute}
          />
        </TouchableOpacity>

        {/* Zoomable image */}
        <ScrollView style={{flex: 1, padding: scale > 1 ? 100 : 0}}>
          <ScrollView
            style={{flex: 1, padding: scale > 1 ? 100 : 0}}
            horizontal={true}>
            <View style={styles.imageContainer} {...panResponder.panHandlers}>
              <Animated.View
                style={[
                  styles.animatedImageContainer,
                  {
                    transform: [
                      {scale: scaleValue},
                      {translateX: translateX},
                      {translateY: translateY},
                    ],
                  },
                ]}>
                <TouchableOpacity
                  activeOpacity={1}
                  onPress={handleDoubleTap}
                  style={styles.imageWrapper}>
                  <FastImage
                    source={{uri: imageUri}}
                    style={styles.image}
                    resizeMode={FastImage.resizeMode.contain}
                  />
                </TouchableOpacity>
              </Animated.View>
            </View>
          </ScrollView>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  closeButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 1000,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    padding: 8,
  },
  imageContainer: {
    width: screenWidth,
    height: screenHeight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  animatedImageContainer: {
    width: screenWidth,
    height: screenHeight * 0.8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageWrapper: {
    width: '100%',
    height: '100%',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  zoomIndicator: {
    position: 'absolute',
    top: 100,
    left: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    zIndex: 1000,
  },
  zoomText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  scrollHint: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 10,
    textAlign: 'center',
    marginTop: 2,
  },
});

export default ZoomableImageModal;
