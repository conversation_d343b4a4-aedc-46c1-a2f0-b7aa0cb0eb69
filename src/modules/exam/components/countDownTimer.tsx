import React, {useEffect, useState} from 'react';
import {Text, StyleSheet, TextStyle} from 'react-native';

interface CountdownTimerProps {
  initialMinutes: number; // Initial countdown time in minutes
  onTimeUp: () => void; // Callback when the timer reaches 0
  textStyle?: TextStyle;
}

const CountdownTimer: React.FC<CountdownTimerProps> = ({
  initialMinutes,
  onTimeUp,
  textStyle = {},
}) => {
  const [timeLeft, setTimeLeft] = useState(initialMinutes * 60); // Convert minutes to seconds

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(prevTime => {
        if (prevTime <= 1) {
          clearInterval(timer);
          onTimeUp(); // Trigger callback when time is up
          return 0;
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => clearInterval(timer); // Cleanup on unmount
  }, [onTimeUp]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  return (
    <Text style={{...styles.timer, ...textStyle}}>{formatTime(timeLeft)}</Text>
  );
};

const styles = StyleSheet.create({
  timer: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'black',
  },
});

export default CountdownTimer;
