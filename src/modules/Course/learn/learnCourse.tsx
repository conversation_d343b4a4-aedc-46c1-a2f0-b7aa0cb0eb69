import {Image, StyleSheet, Text, View} from 'react-native';
import {
  A<PERSON><PERSON><PERSON>on,
  FBottomSheet,
  showSnackbar,
  ComponentStatus,
} from 'wini-mobile-components';
import {navigateBack} from '../../../router/router';
import {ColorThemes} from '../../../assets/skin/colors';
import {useRoute, RouteProp} from '@react-navigation/native';
import {useEffect, useRef, useState, useCallback, useMemo} from 'react';
import {useDispatch} from 'react-redux';
import {AppDispatch} from '../../../redux/store/store';
import DocumentTab, {LoadingUI} from './documentTab';

import VideoPlayerWithFullscreen from '../components/VideoPlayerWithFullscreen';
import {LessonActions} from '../../../redux/reducers/proccessLessonReducer';
import {CourseDA} from '../da';
import {SafeAreaView} from 'react-native-safe-area-context';
import ConfigAPI from '../../../Config/ConfigAPI';
import {TypoSkin} from '../../../assets/skin/typography';
import {BaseDA} from '../../../base/BaseDA';
import {TabBar, TabView} from 'react-native-tab-view';
import VideoListTab from './videoListTab';

// TypeScript Interfaces
interface StepData {
  lessonId: string;
  courseId: string;
  stepId: string;
  stepOrder: number;
  isLastStep: boolean;
  lessonIndex: number;
  courseCustomerId: string;
  stepType?: string;
}

interface LessonData {
  Id: string;
  Name: string;
  Video?: string;
  Document?: string;
  Introduction?: string;
  Hours?: number;
}

interface VideoData {
  Id: string;
  Url: string;
  Name: string;
  Size?: number;
  duration?: string;
  isCompleted?: boolean;
}

interface RouteParams {
  Step: StepData;
  type: 'Video' | 'Document';
}

type LearnCourseRouteProp = RouteProp<{params: RouteParams}, 'params'>;

export default function LearnCourse() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [videoLoading, setVideoLoading] = useState(false);
  const route = useRoute<LearnCourseRouteProp>();
  const {Step, type} = route.params;

  const [lessonData, setLessonData] = useState<LessonData | null>(null);
  const dispatch: AppDispatch = useDispatch();
  const courseDA = useMemo(() => new CourseDA(), []);

  const fetchData = useCallback(async () => {
    if (!Step?.lessonId) {
      setError('Không tìm thấy thông tin bài học');
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Fetch lesson data
      const lessonResult = await courseDA.getLessonDetail(Step.lessonId);

      if (lessonResult && lessonResult.code === 200 && lessonResult.data) {
        setLessonData(lessonResult.data);
      } else {
        setError('Không thể tải dữ liệu bài học');
        showSnackbar({
          message: 'Không thể tải dữ liệu bài học',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (fetchError) {
      console.error('Error fetching data:', fetchError);
      const errorMessage = 'Có lỗi xảy ra khi tải dữ liệu';
      setError(errorMessage);
      showSnackbar({
        message: errorMessage,
        status: ComponentStatus.ERROR,
      });
    } finally {
      setLoading(false);
    }
  }, [Step?.lessonId, courseDA]);

  // Set initial tab index based on type
  useEffect(() => {
    if (type === 'Document') {
      setTabIndex(0); // Document tab is first (index 0)
    } else {
      setTabIndex(1); // Video tab is second (index 1)
    }
  }, [type]);

  // Fetch data when Step changes
  useEffect(() => {
    fetchData();
  }, [fetchData]);
  useEffect(() => {
    const getProccessVideo = async () => {
      const rs = await courseDA.getProccessVideoLesson(
        Step.courseId,
        Step.lessonId,
      );
      if (rs) {
        // const videoIds = rs[0].DocumentStep ? rs[0].DocumentStep?.split(',') : [];
        setproccessVideo(rs[0]);
      }
    };
    getProccessVideo();
  }, [Step.lessonId, Step.courseId]);

  const dispatchedRef = useRef<Set<number>>(new Set());
  const bottomSheetRef = useRef<any>(null);
  const hasCheckedInitialVideo = useRef<boolean>(false);
  // const playerRef = useRef<VideoPlayerRef>(null);

  const [videos, setVideos] = useState<VideoData[]>([]);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);
  const [completedVideos, setCompletedVideos] = useState<Set<number>>(
    new Set(),
  );
  const [tabIndex, setTabIndex] = useState(type === 'Document' ? 0 : 1);
  const [proccessVideo, setproccessVideo] = useState<any>();

  // Current video for backward compatibility
  const currentVideo = videos[currentVideoIndex] || null;

  // Tab routes - Document tab first as requested
  const tabRoutes = useMemo(
    () => [
      {key: 'document', title: 'Document'},
      {key: 'videos', title: `Video List (${videos.length})`},
    ],
    [videos.length],
  );

  // Handle video selection from video list
  const handleVideoSelect = useCallback(
    (videoIndex: number, _video: VideoData) => {
      setCurrentVideoIndex(videoIndex);
      // Reset progress tracking for new video
      dispatchedRef.current.clear();
    },
    [],
  );

  // Tab render functions
  const renderTabScene = useCallback(
    ({route: tabRoute}: any) => {
      switch (tabRoute.key) {
        case 'document':
          return (
            <DocumentTab
              lessonData={lessonData}
              step={Step}
              isActive={tabIndex === 0}
            />
          );
        case 'videos':
          return (
            <VideoListTab
              videos={videos}
              videoLoading={videoLoading}
              currentVideoIndex={currentVideoIndex}
              onVideoSelect={handleVideoSelect}
              Step={Step}
            />
          );
        default:
          return null;
      }
    },
    [
      lessonData,
      Step,
      tabIndex,
      currentVideoIndex,
      handleVideoSelect,
      videos,
      videoLoading,
    ],
  );

  const renderTabBar = useCallback(
    (props: any) => (
      <TabBar
        {...props}
        indicatorStyle={styles.tabIndicator}
        style={styles.tabBar}
        tabStyle={styles.tabStyle}
        activeColor={ColorThemes.light.Primary_Color_Main}
        inactiveColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
      />
    ),
    [],
  );

  // Reset progress tracking when Step changes
  useEffect(() => {
    dispatchedRef.current.clear();
    hasCheckedInitialVideo.current = false; // Reset để kiểm tra lại video cho lesson mới
  }, [Step.lessonId, Step.stepId]);

  const handleVideoProgress = useCallback(
    (percent: number) => {
      if (!Step) {
        return;
      }

      // Mark video as completed when reaching 90%
      if (percent >= 100) {
        setCompletedVideos(prev => new Set([...prev, currentVideoIndex]));
      }

      // Auto-next to next video when reaching 100%
      if (percent >= 100 && currentVideoIndex < videos.length - 1) {
        setTimeout(() => {
          setCurrentVideoIndex(prev => prev + 1);
          dispatchedRef.current.clear(); // Reset progress tracking
        }, 1000); // Small delay before auto-next
      }

      // Xử lý các mốc tiến độ
      const MILESTONES = [90, 100];
      for (let milestone of MILESTONES) {
        if (percent >= milestone && !dispatchedRef.current.has(milestone)) {
          console.log('Video progress milestone reached:', milestone);
          dispatchedRef.current.add(milestone);
          const videoIds = lessonData?.Video?.split(',') ?? [];
          try {
            dispatch(
              LessonActions.updateProcess({
                Id: Step.lessonId,
                CourseId: Step.courseId,
                stepId: Step.stepId,
                stepOrder: Step.stepOrder,
                PercentCompleted: percent >= 90 ? 100 : milestone,
                isLastStep: Step.isLastStep,
                lessonIndex: Step.lessonIndex,
                courseCustomerId: Step.courseCustomerId,
                Type: 'Video',
                Name: videoIds ? videoIds[currentVideoIndex] : '',
              }),
            );
          } catch (progressError) {
            console.error('Error updating progress:', progressError);
          }
        }
      }
    },
    [Step, dispatch, currentVideoIndex, videos.length, lessonData?.Video],
  );

  useEffect(() => {
    const loadVideos = async () => {
      if (!lessonData?.Video) {
        setVideos([]);
        return;
      }

      setVideoLoading(true);
      try {
        // Parse video IDs from comma-separated string
        const videoIds = lessonData.Video.split(',').map((id: string) =>
          id.trim(),
        );

        if (videoIds.length > 0) {
          const rs = await BaseDA.getFilesInfor(videoIds);
          if (rs && rs.data && rs.data.length > 0) {
            // Kiểm tra video nào đã hoàn thành từ proccessVideo
            const completedVideoIds = proccessVideo?.DocumentStep
              ? proccessVideo.DocumentStep.split(',').map((id: string) =>
                  id.trim(),
                )
              : [];

            // Re-enable completion check safely
            const videoList = rs.data.map((item: any, index: number) => {
              const isCompleted = completedVideoIds.includes(item.Id);
              return {
                Id: item.Id || `video-${index}`,
                Url: item.Url || '',
                Name: item.Name || `Video ${index + 1}`,
                Size: item.Size || 0,
                duration: formatDuration(item.Size),
                isCompleted: isCompleted,
              };
            });

            // Cập nhật completedVideos state dựa trên dữ liệu từ server
            const newCompletedVideos = new Set<number>();
            videoList.forEach((video: VideoData, index: number) => {
              if (video.isCompleted) {
                newCompletedVideos.add(index);
              }
            });
            setCompletedVideos(newCompletedVideos);

            setVideos(videoList);
          } else {
            setVideos([]);
            console.warn('No video data received');
          }
        }
      } catch (videoError) {
        console.error('Error loading videos:', videoError);
        setVideos([]);
      } finally {
        setVideoLoading(false);
      }
    };
    loadVideos();
  }, [lessonData, proccessVideo]);

  // Tự động chuyển đến video chưa hoàn thành đầu tiên khi videos được load lần đầu
  useEffect(() => {
    // Only run once when videos are first loaded
    if (videos.length > 0 && !hasCheckedInitialVideo.current) {
      hasCheckedInitialVideo.current = true;

      try {
        // Safely check if current video is completed
        const currentVideoData = videos[currentVideoIndex];
        if (currentVideoData && currentVideoData.isCompleted) {
          const firstIncompleteIndex = videos.findIndex(
            (video: VideoData) => !video.isCompleted,
          );
          if (
            firstIncompleteIndex !== -1 &&
            firstIncompleteIndex !== currentVideoIndex
          ) {
            console.log(
              `Auto-switching from completed video ${currentVideoIndex} to incomplete video ${firstIncompleteIndex}`,
            );
            setCurrentVideoIndex(firstIncompleteIndex);
          }
        }
      } catch (autoSwitchError) {
        console.warn('Error in auto-switch logic:', autoSwitchError);
      }
    }
  }, [videos.length]); // Only depend on videos.length to avoid infinite loops

  // Show error state if there's an error
  if (error) {
    return (
      <View style={styles.container}>
        <SafeAreaView edges={['top']} />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <AppButton
            title="Thử lại"
            onPress={fetchData}
            containerStyle={styles.retryButton}
          />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FBottomSheet ref={bottomSheetRef} />
      <SafeAreaView edges={['top']} />
      {loading ? (
        <View style={styles.loadingContainer} pointerEvents="none">
          <LoadingUI />
        </View>
      ) : (
        <View style={styles.videoContainer}>
          <AppButton
            prefixIcon={'outline/user interface/e-remove'}
            prefixIconSize={20}
            backgroundColor={
              ColorThemes.light.Neutral_Background_Color_Absolute
            }
            textColor={ColorThemes.light.Neutral_Text_Color_Title}
            borderColor="transparent"
            containerStyle={styles.closeButton}
            onPress={navigateBack}
          />
          {renderVideoContent()}
        </View>
      )}
      {/* //thêm tiêu đề của lesson */}
      <View
        style={{
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
          // marginBottom: 8,
          paddingHorizontal: 16,
          marginTop: 8,
          // paddingVertical: 8,
        }}>
        <Text
          style={{
            ...TypoSkin.heading6,
            color: ColorThemes.light.Neutral_Text_Color_Title,
          }}>
          {lessonData?.Name ?? ''}
        </Text>
      </View>
      <TabView
        navigationState={{index: tabIndex, routes: tabRoutes}}
        renderScene={renderTabScene}
        onIndexChange={setTabIndex}
        renderTabBar={renderTabBar}
        style={styles.tabView}
        swipeEnabled={false}
      />
    </View>
  );

  function renderVideoContent() {
    if (videoLoading) {
      return (
        <View style={styles.placeholderContainer}>
          <LoadingUI style={{height: 200}} />
        </View>
      );
    }

    if (currentVideo?.Url) {
      return (
        <View
          style={
            type === 'Document' ? styles.documentVideoContainer : undefined
          }>
          {type === 'Document' && (
            <View style={styles.placeholderContainer}>
              <Image
                style={styles.placeholderImage}
                source={require('../../../assets/appstore.png')}
              />
            </View>
          )}
          <VideoPlayerWithFullscreen
            key={`video-player-${currentVideo.Id || 'unknown'}-lesson-${
              Step.lessonId
            }-index-${currentVideoIndex}-step-${Step.stepId}`}
            source={ConfigAPI.url.replace('/api/', '') + currentVideo.Url}
            onProgressPercent={handleVideoProgress}
            onLoad={time => {
              console.log('Video loaded, duration:', time);
            }}
          />
        </View>
      );
    }

    return (
      <View style={styles.placeholderContainer}>
        <Image
          style={styles.placeholderImage}
          source={require('../../../assets/appstore.png')}
        />
        <Text style={styles.noVideoText}>Không có video</Text>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
  },
  header: {
    backgroundColor: ColorThemes.light.transparent,
    zIndex: 111,
    position: 'absolute',
  },
  loadingContainer: {
    height: 200,
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoContainer: {
    height: 'auto',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
  },
  normalVideoContainer: {
    position: 'relative',
    width: '100%',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    ...TypoSkin.body1,
    color: ColorThemes.light.Error_Color_Main,
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    marginTop: 16,
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    left: 16,
    zIndex: 111,
    paddingHorizontal: 12,
    borderRadius: 100,
    width: 40,
    height: 40,
  },
  documentVideoContainer: {
    height: 200,
    overflow: 'hidden',
  },
  placeholderContainer: {
    height: 200,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  noVideoText: {
    ...TypoSkin.body2,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginTop: 8,
  },
  tabIndicator: {
    backgroundColor: ColorThemes.light.Primary_Color_Main,
    height: 1.5,
  },
  tabStyle: {
    paddingHorizontal: 4,
    paddingTop: 0,
    alignItems: 'center',
  },
  tabBar: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    height: 45,
    elevation: 0,
  },
  mainContainer: {
    gap: 16,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  mainContent: {
    flex: 1,
  },
  img: {
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  titleStyle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#18181B',
  },
  subTitleStyle: {
    fontSize: 14,
    color: '#61616B',
  },
  bodyContentStyle: {
    fontSize: 14,
    fontWeight: '400',
    color: '#313135',
  },
  tabView: {
    flex: 1,
  },
});

const formatDuration = (size?: number): string => {
  if (!size) {
    return '00:00';
  }
  const estimatedMinutes = Math.floor((size / 1024 / 1024) * 2); // 2 minutes per MB
  const minutes = Math.floor(estimatedMinutes);
  const seconds = Math.floor((estimatedMinutes % 1) * 60);
  return `${minutes.toString().padStart(2, '0')}:${seconds
    .toString()
    .padStart(2, '0')}`;
};
