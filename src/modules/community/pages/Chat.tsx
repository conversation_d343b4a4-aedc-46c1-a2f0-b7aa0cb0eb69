import {useNavigation, DrawerActions} from '@react-navigation/native';
import {FlatList, Image, Text, TouchableOpacity, View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  ListTile,
  SkeletonImage,
  TextField,
  Winicon,
} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {ScrollView} from 'react-native-gesture-handler';
import {navigate, RootScreen} from '../../../router/router';
import ConfigAPI from '../../../Config/ConfigAPI';
import {TypoSkin} from '../../../assets/skin/typography';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {useEffect, useRef, useState} from 'react';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import EmptyPage from '../../../Screen/emptyPage';
import {CustomerDA} from '../../customer/da';
import {LogoImg} from '../../../Screen/Page/Home';

export const ProfileView = () => {
  const user = useSelectorCustomerState().data;

  return (
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
      }}>
      <TouchableOpacity
        onPress={() => {
          navigate(RootScreen.ProfileCommunity, {Id: user?.Id});
        }}>
        {user?.AvatarUrl ? (
          <SkeletonImage
            key={user?.AvatarUrl}
            source={
              user?.AvatarUrl
                ? {uri: ConfigAPI.urlImg + user?.AvatarUrl}
                : require('../../../assets/appstore.png')
            }
            style={{
              width: 32,
              height: 32,
              borderRadius: 50,
              backgroundColor: '#f0f0f0',
              borderColor: ColorThemes.light.Neutral_Border_Color_Main,
              borderWidth: 1,
            }}
          />
        ) : (
          <View
            style={{
              width: 32,
              height: 32,
              borderRadius: 50,
              backgroundColor: ColorThemes.light.Primary_Color_Main,
              alignItems: 'center',
              justifyContent: 'center',
            }}>
            <Text
              style={{
                ...TypoSkin.heading7,
                color: ColorThemes.light.Neutral_Background_Color_Absolute,
              }}>
              {user?.Name ? user.Name.charAt(0).toUpperCase() : 'U'}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    </View>
  );
};

export default function Chat() {
  const navigation = useNavigation<any>();
  const [searchValue, setSearchValue] = useState('');
  const user = useSelectorCustomerState().data;
  const [listFriend, setlistFriend] = useState<Array<any>>([]);
  const customerDA = new CustomerDA();
  const bottomSheetRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (searchValue && Array.isArray(listFriend)) {
      // Lọc danh sách thành viên theo tên
      const filteredMembers = listFriend.filter((member: any) =>
        member.Name.toLowerCase().includes(searchValue.toLowerCase()),
      );
      setlistFriend(filteredMembers);
    }
  }, [searchValue]);

  useEffect(() => {
    getListFriend();
  }, []);

  const getListFriend = async () => {
    if (!user) return;
    setIsLoading(true);
    const result = await customerDA.getListFriend(user.Id);
    if (result) {
      setlistFriend(result);
    }
    setIsLoading(false);
  };

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      {/* header */}
      <ListTile
        style={{
          padding: 0,
          paddingBottom: 8,
          paddingHorizontal: 16,
        }}
        isClickLeading
        leading={
          <TouchableOpacity
            onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
            style={{padding: 4}}>
            <LogoImg />
          </TouchableOpacity>
        }
        title="Chat"
        trailing={<ProfileView />}
        bottom={
          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              height: 56,
              gap: 8,
              paddingTop: 16,
              paddingBottom: 16,
            }}>
            <TextField
              style={{paddingHorizontal: 16, flex: 1, height: 40}}
              onChange={async (vl: string) => {
                setSearchValue(vl.trim());
              }}
              value={searchValue}
              placeholder="Tìm kiếm..."
              prefix={
                <Winicon
                  src="outline/development/zoom"
                  size={14}
                  color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                />
              }
            />
          </View>
        }
      />
      {/* content */}
      <FlatList
        data={listFriend}
        style={{height: '100%'}}
        keyExtractor={(item, index) => index.toString()}
        ListEmptyComponent={() => {
          if (isLoading) {
            return (
              <View style={{flex: 1, paddingTop: 16, gap: 16}}>
                <SkeletonPlaceCard />
                <SkeletonPlaceCard />
                <SkeletonPlaceCard />
              </View>
            );
          }
          return <EmptyPage title="Không có dữ liệu" />;
        }}
        renderItem={({item, index}) => {
          return (
            <ListTile
              key={index}
              leading={
                <SkeletonImage
                  key={item?.AvatarUrl}
                  source={{
                    uri: item?.AvatarUrl
                      ? `${ConfigAPI.urlImg + item?.AvatarUrl}`
                      : 'https://placehold.co/48/FFFFFF/000000/png',
                  }}
                  style={{
                    width: 48,
                    height: 48,
                    borderRadius: 50,
                    backgroundColor: '#f0f0f0',
                  }}
                />
              }
              title={
                <Text
                  style={{
                    ...TypoSkin.heading7,
                    color: ColorThemes.light.Neutral_Text_Color_Title,
                  }}
                  numberOfLines={1}>
                  {item.Name}
                </Text>
              }
              subtitle={item.Description ?? ''}
              subTitleStyle={{
                ...TypoSkin.subtitle3,
                color: ColorThemes.light.Neutral_Text_Color_Subtitle,
              }}
              onPress={() => {}}
              trailing={
                <View
                  style={{alignItems: 'center', flexDirection: 'row', gap: 8}}>
                  <TouchableOpacity style={{padding: 4}} onPress={() => {}}>
                    <Winicon
                      src="fill/user interface/phone-call"
                      color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                      size={16}
                    />
                  </TouchableOpacity>
                  <TouchableOpacity style={{padding: 4}} onPress={() => {}}>
                    <Winicon
                      src="fill/user interface/f-chat"
                      color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                      size={16}
                    />
                  </TouchableOpacity>
                </View>
              }
            />
          );
        }}
      />
    </View>
  );
}

const SkeletonPlaceCard = () => {
  return (
    <SkeletonPlaceholder
      backgroundColor="#f0f0f0"
      highlightColor="#e0e0e0"
      enabled={true}
      speed={800}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          padding: 16,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}>
        {/* Avatar placeholder */}
        <View
          style={{
            width: 48,
            height: 48,
            borderRadius: 50,
            marginRight: 12,
          }}
        />

        {/* Title and subtitle container */}
        <View style={{flex: 1, gap: 8}}>
          {/* Title placeholder */}
          <View
            style={{
              width: '60%',
              height: 16,
              borderRadius: 4,
            }}
          />

          {/* Subtitle placeholder */}
          <View
            style={{
              width: '40%',
              height: 12,
              borderRadius: 4,
            }}
          />
        </View>

        {/* Icons container */}
        <View style={{flexDirection: 'row', gap: 8}}>
          {/* Phone icon placeholder */}
          <View
            style={{
              width: 24,
              height: 24,
              borderRadius: 4,
            }}
          />

          {/* Chat icon placeholder */}
          <View
            style={{
              width: 24,
              height: 24,
              borderRadius: 4,
            }}
          />
        </View>
      </View>
    </SkeletonPlaceholder>
  );
};
